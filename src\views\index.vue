<template>
  <div class="dashboard-editor-container">
    <!-- 调试信息面板 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="24">
        <el-card header="系统状态调试信息">
          <div class="debug-info">
            <p><strong>用户信息:</strong> {{ userInfo }}</p>
            <p><strong>用户角色:</strong> {{ userRoles }}</p>
            <p><strong>侧边栏路由:</strong> {{ sidebarRouters.length }} 个</p>
            <p><strong>OAuth2应用状态:</strong> 加载中: {{ isLoading }}, 应用数量: {{ appList.length }}</p>
            <p><strong>当前路由:</strong> {{ $route.path }}</p>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20">
      <!-- 第三方OAuth2应用模块 -->
      <el-col :span="24">
        <oauth2-apps />
      </el-col>

      <!-- 以下模块暂时注释，等相关依赖解决后再启用 -->
      <!--
      <el-col :span="24">
        <panel-group />
      </el-col>

      <el-col :span="24">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="24" :lg="12">
            <div class="chart-wrapper">
              <line-chart />
            </div>
          </el-col>

          <el-col :xs="24" :sm="24" :lg="6">
            <div class="chart-wrapper">
              <pie-chart />
            </div>
          </el-col>

          <el-col :xs="24" :sm="24" :lg="6">
            <div class="chart-wrapper">
              <raddar-chart />
            </div>
          </el-col>
        </el-row>
      </el-col>

      <el-col :span="24" style="margin-top: 20px;">
        <div class="chart-wrapper">
          <bar-chart />
        </div>
      </el-col>
      -->
    </el-row>
  </div>
</template>

<script>
import OAuth2Apps from '@/components/OAuth2Apps'
import { mapState, mapGetters } from 'vuex'
// 暂时注释掉其他组件的导入，等相关依赖解决后再启用
/*
import PanelGroup from '@/views/dashboard/PanelGroup'
import LineChart from '@/views/dashboard/LineChart'
import PieChart from '@/views/dashboard/PieChart'
import RaddarChart from '@/views/dashboard/RaddarChart'
import BarChart from '@/views/dashboard/BarChart'
*/

export default {
  name: 'Index',
  components: {
    'oauth2-apps': OAuth2Apps,
    // 暂时注释掉其他组件，等相关依赖解决后再启用
    /*
    PanelGroup,
    LineChart,
    PieChart,
    RaddarChart,
    BarChart
    */
  },
  computed: {
    ...mapState('user', ['name', 'roles']),
    ...mapState('oauth2', ['accessibleApps']),
    ...mapGetters(['sidebarRouters']),
    ...mapGetters('oauth2', ['appList', 'isLoading']),
    userInfo() {
      return {
        name: this.name,
        id: this.$store.state.user.id
      }
    },
    userRoles() {
      return this.roles
    }
  },
  mounted() {
    console.log('首页组件已挂载')
    console.log('用户信息:', this.userInfo)
    console.log('用户角色:', this.userRoles)
    console.log('侧边栏路由:', this.sidebarRouters)
    console.log('OAuth2应用:', this.appList)
  }
}
</script>

<style lang="scss" scoped>
.dashboard-editor-container {
  padding: 32px;
  background-color: rgb(240, 242, 245);
  position: relative;
}

.debug-info {
  font-size: 14px;
  line-height: 1.6;

  p {
    margin: 8px 0;
    padding: 4px 0;
    border-bottom: 1px solid #eee;
  }

  strong {
    color: #409EFF;
    margin-right: 8px;
  }
}

.chart-wrapper {
  background-color: #fff;
  padding: 16px 16px;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

@media (max-width:1024px) {
  .chart-wrapper {
    padding: 8px;
  }
}
</style>
